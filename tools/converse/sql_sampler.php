<?php

// open ssh tunnel to db server & forward sql port before running!

// load env
require_once __DIR__ . '/../../vendor/autoload.php';

use Symfony\Component\Dotenv\Dotenv;

$dotenv = new Dotenv();
$dotenv->load(__DIR__ . '/../../.env');


# TODO: Add cli args for the 3 query params
// Pull from multiple tables or schema versions?
// Detect changes across samples (data drift detection)?

// database access
$host = '127.0.0.1';
$port = '3307';
$dbname = $_ENV['DB_NAME'];
$user = $_ENV['DB_USER'];
$password = $_ENV['DB_PASSWORD'];

// query
$modulus = 1000;
$targetBucket = 1; // change this number to get a different sample
// $targetBucket = rand(0, $modulus - 1);
$sampleSize = 5000;

// output
$outputFile = "cv_slice_$sampleSize.csv";

try {
    // Set up PDO connection
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $user, $password);

    // Set error mode
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Prepare your query
    $sql = "
        SELECT *
        FROM Converse
        WHERE MOD(CONV(SUBSTRING(MD5(`STHHLD`), 1, 8), 16, 10), $modulus) = $targetBucket
        LIMIT $sampleSize
    ";

    // Execute the query
    $start = microtime(true);
    $stmt = $pdo->query($sql);
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($rows)) {
        echo "No rows returned.\n";
        exit;
    }

    // time
    $end = microtime(true);
    $time = $end - $start;
    echo "SQL Query Completed in $time seconds.\n";

    // Open CSV file for writing
    $fp = fopen($outputFile, 'w');

    // Write header
    fputcsv($fp, array_keys($rows[0]));

    // Write each row
    foreach ($rows as $row) {
        fputcsv($fp, $row);
    }

    fclose($fp);

    echo "Sample exported to $outputFile\n";

} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage();
    exit;
}
